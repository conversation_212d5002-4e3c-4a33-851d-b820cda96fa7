{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Header: React.FC = () => {\n  return (\n    <header className=\"instagram-header\">\n      <div className=\"instagram-container px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <h1 className=\"text-xl font-bold text-gray-900\">\n              التحولات الاجتماعية في قرطبة\n            </h1>\n          </div>\n          \n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <div className=\"text-sm text-gray-600\">\n              استكشف التحولات الاجتماعية والاقتصادية\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;;;AAEA,MAAM,SAAmB;IACvB,qBACE,iVAAC;QAAO,WAAU;kBAChB,cAAA,iVAAC;YAAI,WAAU;sBACb,cAAA,iVAAC;gBAAI,WAAU;;kCACb,iVAAC;wBAAI,WAAU;kCACb,cAAA,iVAAC;4BAAG,WAAU;sCAAkC;;;;;;;;;;;kCAKlD,iVAAC;wBAAI,WAAU;kCACb,cAAA,iVAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;KApBM;uCAsBS", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/SearchBar.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface SearchBarProps {\n  searchTerm: string;\n  onSearchChange: (term: string) => void;\n}\n\nconst SearchBar: React.FC<SearchBarProps> = ({ searchTerm, onSearchChange }) => {\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          placeholder=\"ابحث عن التحولات الاجتماعية...\"\n          value={searchTerm}\n          onChange={(e) => onSearchChange(e.target.value)}\n          className=\"search-input w-full pr-10 pl-4\"\n        />\n        <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n          <svg\n            className=\"w-4 h-4 text-gray-500\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            />\n          </svg>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SearchBar;\n"], "names": [], "mappings": ";;;;;;AAOA,MAAM,YAAsC;QAAC,EAAE,UAAU,EAAE,cAAc,EAAE;IACzE,qBACE,iVAAC;QAAI,WAAU;kBACb,cAAA,iVAAC;YAAI,WAAU;;8BACb,iVAAC;oBACC,MAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC9C,WAAU;;;;;;8BAEZ,iVAAC;oBAAI,WAAU;8BACb,cAAA,iVAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,iVAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/CategoryFilter.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CategoryFilterProps {\n  selectedCategory: string;\n  onCategoryChange: (category: string) => void;\n}\n\nconst categories = [\n  { id: 'all', name: 'جميع الفئات', description: 'عرض جميع التحولات' },\n  { id: 'social-welfare', name: 'برامج الرعاية الاجتماعية', description: 'برامج الدعم الاجتماعي والرعاية' },\n  { id: 'economic-support', name: 'المبادرات الاقتصادية', description: 'المبادرات والدعم الاقتصادي' },\n  { id: 'private-transport', name: 'خدمات النقل الخاص', description: 'خدمات النقل والمواصلات الخاصة' }\n];\n\nconst CategoryFilter: React.FC<CategoryFilterProps> = ({ selectedCategory, onCategoryChange }) => {\n  return (\n    <div className=\"flex flex-wrap gap-3 justify-center\">\n      {categories.map((category) => (\n        <button\n          key={category.id}\n          onClick={() => onCategoryChange(category.id)}\n          className={`category-filter ${\n            selectedCategory === category.id ? '' : 'inactive'\n          }`}\n          title={category.description}\n        >\n          {category.name}\n        </button>\n      ))}\n    </div>\n  );\n};\n\nexport default CategoryFilter;\n"], "names": [], "mappings": ";;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,MAAM;QAAe,aAAa;IAAoB;IACnE;QAAE,IAAI;QAAkB,MAAM;QAA4B,aAAa;IAAiC;IACxG;QAAE,IAAI;QAAoB,MAAM;QAAwB,aAAa;IAA6B;IAClG;QAAE,IAAI;QAAqB,MAAM;QAAqB,aAAa;IAAgC;CACpG;AAED,MAAM,iBAAgD;QAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;IAC3F,qBACE,iVAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,yBACf,iVAAC;gBAEC,SAAS,IAAM,iBAAiB,SAAS,EAAE;gBAC3C,WAAW,AAAC,mBAEX,OADC,qBAAqB,SAAS,EAAE,GAAG,KAAK;gBAE1C,OAAO,SAAS,WAAW;0BAE1B,SAAS,IAAI;eAPT,SAAS,EAAE;;;;;;;;;;AAY1B;KAjBM;uCAmBS", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/PostCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface Post {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  tags: string[];\n  author: string;\n  date: string;\n  likes: number;\n  comments: number;\n  image?: string;\n}\n\ninterface PostCardProps {\n  post: Post;\n}\n\nconst PostCard: React.FC<PostCardProps> = ({ post }) => {\n  const getCategoryName = (category: string) => {\n    switch (category) {\n      case 'social-welfare':\n        return 'برامج الرعاية الاجتماعية';\n      case 'economic-support':\n        return 'المبادرات الاقتصادية';\n      case 'private-transport':\n        return 'خدمات النقل الخاص';\n      default:\n        return 'عام';\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'social-welfare':\n        return 'bg-blue-100 text-blue-800';\n      case 'economic-support':\n        return 'bg-green-100 text-green-800';\n      case 'private-transport':\n        return 'bg-purple-100 text-purple-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"instagram-post\">\n      {/* Post Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">\n                {post.author.charAt(0)}\n              </span>\n            </div>\n            <div>\n              <p className=\"font-semibold text-sm\">{post.author}</p>\n              <p className=\"text-gray-500 text-xs\">{post.date}</p>\n            </div>\n          </div>\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(post.category)}`}>\n            {getCategoryName(post.category)}\n          </span>\n        </div>\n      </div>\n\n      {/* Post Content */}\n      <div className=\"p-4\">\n        <h3 className=\"font-bold text-lg mb-2 text-gray-900\">{post.title}</h3>\n        <p className=\"text-gray-700 mb-3 leading-relaxed\">{post.description}</p>\n        \n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-3\">\n          {post.tags.map((tag, index) => (\n            <span\n              key={index}\n              className=\"bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs\"\n            >\n              #{tag}\n            </span>\n          ))}\n        </div>\n      </div>\n\n      {/* Post Actions */}\n      <div className=\"px-4 py-3 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <button className=\"flex items-center space-x-1 space-x-reverse text-gray-600 hover:text-red-500 transition-colors\">\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n              <span className=\"text-sm\">{post.likes}</span>\n            </button>\n            <button className=\"flex items-center space-x-1 space-x-reverse text-gray-600 hover:text-blue-500 transition-colors\">\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n              </svg>\n              <span className=\"text-sm\">{post.comments}</span>\n            </button>\n          </div>\n          <button className=\"text-gray-600 hover:text-gray-800 transition-colors\">\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PostCard;\n"], "names": [], "mappings": ";;;;;;AAmBA,MAAM,WAAoC;QAAC,EAAE,IAAI,EAAE;IACjD,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,iVAAC;QAAI,WAAU;;0BAEb,iVAAC;gBAAI,WAAU;0BACb,cAAA,iVAAC;oBAAI,WAAU;;sCACb,iVAAC;4BAAI,WAAU;;8CACb,iVAAC;oCAAI,WAAU;8CACb,cAAA,iVAAC;wCAAK,WAAU;kDACb,KAAK,MAAM,CAAC,MAAM,CAAC;;;;;;;;;;;8CAGxB,iVAAC;;sDACC,iVAAC;4CAAE,WAAU;sDAAyB,KAAK,MAAM;;;;;;sDACjD,iVAAC;4CAAE,WAAU;sDAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;sCAGnD,iVAAC;4BAAK,WAAW,AAAC,8CAA6E,OAAhC,iBAAiB,KAAK,QAAQ;sCAC1F,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,iVAAC;gBAAI,WAAU;;kCACb,iVAAC;wBAAG,WAAU;kCAAwC,KAAK,KAAK;;;;;;kCAChE,iVAAC;wBAAE,WAAU;kCAAsC,KAAK,WAAW;;;;;;kCAGnE,iVAAC;wBAAI,WAAU;kCACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,iVAAC;gCAEC,WAAU;;oCACX;oCACG;;+BAHG;;;;;;;;;;;;;;;;0BAUb,iVAAC;gBAAI,WAAU;0BACb,cAAA,iVAAC;oBAAI,WAAU;;sCACb,iVAAC;4BAAI,WAAU;;8CACb,iVAAC;oCAAO,WAAU;;sDAChB,iVAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,iVAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,iVAAC;4CAAK,WAAU;sDAAW,KAAK,KAAK;;;;;;;;;;;;8CAEvC,iVAAC;oCAAO,WAAU;;sDAChB,iVAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,iVAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,iVAAC;4CAAK,WAAU;sDAAW,KAAK,QAAQ;;;;;;;;;;;;;;;;;;sCAG5C,iVAAC;4BAAO,WAAU;sCAChB,cAAA,iVAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,iVAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;KA7FM;uCA+FS", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/data/socialTransfers.ts"], "sourcesContent": ["export interface SocialTransfer {\n  id: number;\n  title: string;\n  description: string;\n  category: 'social-welfare' | 'economic-support' | 'private-transport';\n  tags: string[];\n  author: string;\n  date: string;\n  likes: number;\n  comments: number;\n  image?: string;\n}\n\nexport const socialTransfersData: SocialTransfer[] = [\n  {\n    id: 1,\n    title: \"برنامج الدعم الاجتماعي للأسر المحتاجة في قرطبة\",\n    description: \"يهدف هذا البرنامج إلى تقديم الدعم المالي والاجتماعي للأسر ذات الدخل المحدود في مدينة قرطبة. يشمل البرنامج توفير المساعدات النقدية الشهرية، والدعم الغذائي، وبرامج التدريب المهني لتحسين فرص العمل. كما يتضمن خدمات الرعاية الصحية الأساسية والدعم التعليمي للأطفال.\",\n    category: \"social-welfare\",\n    tags: [\"دعم_اجتماعي\", \"أسر_محتاجة\", \"مساعدات_مالية\", \"قرطبة\"],\n    author: \"د. أحمد الأندلسي\",\n    date: \"منذ 3 أيام\",\n    likes: 245,\n    comments: 18\n  },\n  {\n    id: 2,\n    title: \"مبادرة دعم المشاريع الصغيرة والمتوسطة\",\n    description: \"تركز هذه المبادرة على تمكين رواد الأعمال الشباب في قرطبة من خلال تقديم القروض الميسرة والاستشارات التجارية. تشمل المبادرة برامج تدريبية في إدارة الأعمال، والتسويق الرقمي، والتخطيط المالي. كما توفر مساحات عمل مشتركة ومعارض لعرض المنتجات المحلية.\",\n    category: \"economic-support\",\n    tags: [\"مشاريع_صغيرة\", \"ريادة_أعمال\", \"قروض_ميسرة\", \"تدريب\"],\n    author: \"مريم القرطبية\",\n    date: \"منذ 5 أيام\",\n    likes: 189,\n    comments: 24\n  },\n  {\n    id: 3,\n    title: \"خدمة النقل المجتمعي للمناطق النائية\",\n    description: \"خدمة نقل مبتكرة تهدف إلى ربط المناطق النائية في قرطبة بالمركز الحضري. تستخدم الخدمة حافلات صغيرة صديقة للبيئة وتطبيق ذكي لحجز الرحلات. تساعد هذه الخدمة في تحسين الوصول إلى الخدمات الأساسية مثل المستشفيات والمدارس ومراكز التسوق.\",\n    category: \"private-transport\",\n    tags: [\"نقل_مجتمعي\", \"مناطق_نائية\", \"تطبيق_ذكي\", \"بيئة\"],\n    author: \"عبد الرحمن المهندس\",\n    date: \"منذ أسبوع\",\n    likes: 156,\n    comments: 12\n  },\n  {\n    id: 4,\n    title: \"برنامج الرعاية الصحية المنزلية لكبار السن\",\n    description: \"يوفر هذا البرنامج خدمات الرعاية الصحية المنزلية لكبار السن في قرطبة، بما في ذلك الفحوصات الطبية الدورية، وإدارة الأدوية، والعلاج الطبيعي. يهدف البرنامج إلى تحسين جودة الحياة لكبار السن وتقليل الحاجة للإقامة في المستشفيات.\",\n    category: \"social-welfare\",\n    tags: [\"كبار_السن\", \"رعاية_منزلية\", \"صحة\", \"جودة_الحياة\"],\n    author: \"د. فاطمة الطبيبة\",\n    date: \"منذ أسبوعين\",\n    likes: 298,\n    comments: 31\n  },\n  {\n    id: 5,\n    title: \"صندوق دعم الابتكار التكنولوجي\",\n    description: \"صندوق استثماري يهدف إلى دعم الشركات الناشئة في مجال التكنولوجيا في قرطبة. يقدم الصندوق التمويل الأولي، والإرشاد من خبراء الصناعة، وإمكانية الوصول إلى شبكة واسعة من المستثمرين. يركز على الحلول التقنية التي تخدم المجتمع المحلي.\",\n    category: \"economic-support\",\n    tags: [\"ابتكار\", \"تكنولوجيا\", \"شركات_ناشئة\", \"استثمار\"],\n    author: \"محمد التقني\",\n    date: \"منذ 10 أيام\",\n    likes: 167,\n    comments: 19\n  },\n  {\n    id: 6,\n    title: \"شبكة سيارات الأجرة الكهربائية\",\n    description: \"مشروع رائد لإطلاق أسطول من سيارات الأجرة الكهربائية في قرطبة. يهدف المشروع إلى تقليل التلوث البيئي وتوفير خدمة نقل نظيفة وموثوقة. يتضمن المشروع محطات شحن سريع موزعة في أنحاء المدينة وتطبيق محمول لطلب الخدمة.\",\n    category: \"private-transport\",\n    tags: [\"سيارات_كهربائية\", \"بيئة\", \"نقل_نظيف\", \"تطبيق\"],\n    author: \"سارة البيئية\",\n    date: \"منذ 4 أيام\",\n    likes: 203,\n    comments: 15\n  },\n  {\n    id: 7,\n    title: \"مركز التأهيل المهني للشباب\",\n    description: \"مركز متخصص في تدريب الشباب على المهارات التقنية والحرفية المطلوبة في سوق العمل. يقدم المركز دورات في البرمجة، والتصميم الجرافيكي، والحرف اليدوية التقليدية. كما يوفر برامج التوجيه المهني وربط الخريجين بفرص العمل المناسبة.\",\n    category: \"social-welfare\",\n    tags: [\"تأهيل_مهني\", \"شباب\", \"مهارات_تقنية\", \"توظيف\"],\n    author: \"أستاذ يوسف\",\n    date: \"منذ 6 أيام\",\n    likes: 178,\n    comments: 22\n  },\n  {\n    id: 8,\n    title: \"برنامج دعم المزارعين المحليين\",\n    description: \"مبادرة لدعم المزارعين في المناطق الريفية حول قرطبة من خلال توفير البذور المحسنة، والأسمدة العضوية، والتدريب على التقنيات الزراعية الحديثة. يهدف البرنامج إلى زيادة الإنتاجية وتحسين دخل المزارعين مع الحفاظ على البيئة.\",\n    category: \"economic-support\",\n    tags: [\"زراعة\", \"مزارعين\", \"تقنيات_حديثة\", \"بيئة\"],\n    author: \"م. خالد الزراعي\",\n    date: \"منذ 8 أيام\",\n    likes: 134,\n    comments: 16\n  },\n  {\n    id: 9,\n    title: \"خدمة النقل المدرسي الآمن\",\n    description: \"خدمة نقل مخصصة للطلاب تضمن وصولهم الآمن إلى المدارس. تتميز الخدمة بحافلات مجهزة بأنظمة أمان متقدمة، وسائقين مدربين، ونظام تتبع GPS لمراقبة الرحلات. تشمل الخدمة أيضاً تطبيق للأهالي لمتابعة رحلات أطفالهم.\",\n    category: \"private-transport\",\n    tags: [\"نقل_مدرسي\", \"أمان\", \"طلاب\", \"تتبع\"],\n    author: \"أبو محمد السائق\",\n    date: \"منذ 12 يوم\",\n    likes: 267,\n    comments: 28\n  },\n  {\n    id: 10,\n    title: \"برنامج الدعم النفسي والاجتماعي\",\n    description: \"برنامج شامل لتقديم الدعم النفسي والاجتماعي لأفراد المجتمع الذين يواجهون تحديات نفسية أو اجتماعية. يتضمن البرنامج جلسات استشارة فردية وجماعية، وورش عمل للتوعية، وخط ساخن للدعم النفسي على مدار الساعة.\",\n    category: \"social-welfare\",\n    tags: [\"دعم_نفسي\", \"استشارة\", \"صحة_نفسية\", \"مجتمع\"],\n    author: \"د. ليلى النفسية\",\n    date: \"منذ 9 أيام\",\n    likes: 312,\n    comments: 35\n  }\n];\n"], "names": [], "mappings": ";;;;AAaO,MAAM,sBAAwC;IACnD;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAe;YAAc;YAAiB;SAAQ;QAC7D,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAgB;YAAe;YAAc;SAAQ;QAC5D,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAc;YAAe;YAAa;SAAO;QACxD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAa;YAAgB;YAAO;SAAc;QACzD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAU;YAAa;YAAe;SAAU;QACvD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAmB;YAAQ;YAAY;SAAQ;QACtD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAc;YAAQ;YAAgB;SAAQ;QACrD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAgB;SAAO;QAClD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAa;YAAQ;YAAQ;SAAO;QAC3C,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAY;YAAW;YAAa;SAAQ;QACnD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Header from '@/components/Header';\nimport SearchBar from '@/components/SearchBar';\nimport CategoryFilter from '@/components/CategoryFilter';\nimport PostCard from '@/components/PostCard';\nimport { socialTransfersData } from '@/data/socialTransfers';\n\nexport default function Home() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  const filteredData = socialTransfersData.filter(item => {\n    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;\n\n    return matchesSearch && matchesCategory;\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"instagram-container px-4 py-6\">\n        <div className=\"mb-6\">\n          <SearchBar\n            searchTerm={searchTerm}\n            onSearchChange={setSearchTerm}\n          />\n        </div>\n\n        <div className=\"mb-6\">\n          <CategoryFilter\n            selectedCategory={selectedCategory}\n            onCategoryChange={setSelectedCategory}\n          />\n        </div>\n\n        <div className=\"space-y-6\">\n          {filteredData.length > 0 ? (\n            filteredData.map((item) => (\n              <PostCard key={item.id} post={item} />\n            ))\n          ) : (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500 text-lg\">لا توجد نتائج للبحث الحالي</p>\n              <p className=\"text-gray-400 text-sm mt-2\">جرب البحث بكلمات مختلفة أو غير الفئة المحددة</p>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,6TAAQ,EAAC;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,6TAAQ,EAAC;IAEzD,MAAM,eAAe,4SAAmB,CAAC,MAAM,CAAC,CAAA;QAC9C,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE5F,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QAExE,OAAO,iBAAiB;IAC1B;IAEA,qBACE,iVAAC;QAAI,WAAU;;0BACb,iVAAC,8RAAM;;;;;0BAEP,iVAAC;gBAAK,WAAU;;kCACd,iVAAC;wBAAI,WAAU;kCACb,cAAA,iVAAC,iSAAS;4BACR,YAAY;4BACZ,gBAAgB;;;;;;;;;;;kCAIpB,iVAAC;wBAAI,WAAU;kCACb,cAAA,iVAAC,sSAAc;4BACb,kBAAkB;4BAClB,kBAAkB;;;;;;;;;;;kCAItB,iVAAC;wBAAI,WAAU;kCACZ,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,qBAChB,iVAAC,gSAAQ;gCAAe,MAAM;+BAAf,KAAK,EAAE;;;;sDAGxB,iVAAC;4BAAI,WAAU;;8CACb,iVAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,iVAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD;GAhDwB;KAAA", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,8KACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}