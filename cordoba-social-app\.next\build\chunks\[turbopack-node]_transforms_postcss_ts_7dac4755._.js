module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/OneDrive/Pictures/التحولات في قرطبة/cordoba-social-app/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/1e4ff_02a26fad._.js",
  "build/chunks/[root-of-the-server]__947b7f1a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/OneDrive/Pictures/التحولات في قرطبة/cordoba-social-app/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];