import React from 'react';

interface CategoryFilterProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const categories = [
  { id: 'all', name: 'جميع الفئات', description: 'عرض جميع التحولات' },
  { id: 'social-welfare', name: 'برامج الرعاية الاجتماعية', description: 'برامج الدعم الاجتماعي والرعاية' },
  { id: 'economic-support', name: 'المبادرات الاقتصادية', description: 'المبادرات والدعم الاقتصادي' },
  { id: 'private-transport', name: 'خدمات النقل الخاص', description: 'خدمات النقل والمواصلات الخاصة' }
];

const CategoryFilter: React.FC<CategoryFilterProps> = ({ selectedCategory, onCategoryChange }) => {
  return (
    <div className="flex flex-wrap gap-3 justify-center">
      {categories.map((category) => (
        <button
          key={category.id}
          onClick={() => onCategoryChange(category.id)}
          className={`category-filter ${
            selectedCategory === category.id ? '' : 'inactive'
          }`}
          title={category.description}
        >
          {category.name}
        </button>
      ))}
    </div>
  );
};

export default CategoryFilter;
