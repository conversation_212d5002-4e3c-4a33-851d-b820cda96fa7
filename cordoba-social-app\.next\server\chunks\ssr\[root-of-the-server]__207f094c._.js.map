{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Header: React.FC = () => {\n  return (\n    <header className=\"instagram-header\">\n      <div className=\"instagram-container px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-full flex items-center justify-center\">\n                <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center\">\n                  <span className=\"text-purple-600 font-bold text-sm\">ق</span>\n                </div>\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900 arabic-text\">\n                  التحولات الاجتماعية في قرطبة\n                </h1>\n                <p className=\"text-xs text-gray-500 arabic-text\">\n                  استكشف التحولات الاجتماعية والاقتصادية\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n              </svg>\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;;;AAEA,MAAM,SAAmB;IACvB,qBACE,kYAAC;QAAO,WAAU;kBAChB,cAAA,kYAAC;YAAI,WAAU;sBACb,cAAA,kYAAC;gBAAI,WAAU;;kCACb,kYAAC;wBAAI,WAAU;kCACb,cAAA,kYAAC;4BAAI,WAAU;;8CACb,kYAAC;oCAAI,WAAU;8CACb,cAAA,kYAAC;wCAAI,WAAU;kDACb,cAAA,kYAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;8CAGxD,kYAAC;;sDACC,kYAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAG5D,kYAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAOvD,kYAAC;wBAAI,WAAU;;0CACb,kYAAC;gCAAO,WAAU;0CAChB,cAAA,kYAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,kYAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,kYAAC;gCAAO,WAAU;0CAChB,cAAA,kYAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,kYAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,kYAAC;gCAAO,WAAU;0CAChB,cAAA,kYAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,kYAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;uCAEe", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/SearchBar.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface SearchBarProps {\n  searchTerm: string;\n  onSearchChange: (term: string) => void;\n}\n\nconst SearchBar: React.FC<SearchBarProps> = ({ searchTerm, onSearchChange }) => {\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          placeholder=\"ابحث عن التحولات الاجتماعية...\"\n          value={searchTerm}\n          onChange={(e) => onSearchChange(e.target.value)}\n          className=\"search-input w-full pr-10 pl-4\"\n        />\n        <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n          <svg\n            className=\"w-4 h-4 text-gray-500\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            />\n          </svg>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SearchBar;\n"], "names": [], "mappings": ";;;;;;AAOA,MAAM,YAAsC,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE;IACzE,qBACE,kYAAC;QAAI,WAAU;kBACb,cAAA,kYAAC;YAAI,WAAU;;8BACb,kYAAC;oBACC,MAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC9C,WAAU;;;;;;8BAEZ,kYAAC;oBAAI,WAAU;8BACb,cAAA,kYAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,kYAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;uCAEe", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/CategoryFilter.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CategoryFilterProps {\n  selectedCategory: string;\n  onCategoryChange: (category: string) => void;\n}\n\nconst categories = [\n  { id: 'all', name: 'جميع الفئات', description: 'عرض جميع التحولات' },\n  { id: 'social-welfare', name: 'برامج الرعاية الاجتماعية', description: 'برامج الدعم الاجتماعي والرعاية' },\n  { id: 'economic-support', name: 'المبادرات الاقتصادية', description: 'المبادرات والدعم الاقتصادي' },\n  { id: 'private-transport', name: 'خدمات النقل الخاص', description: 'خدمات النقل والمواصلات الخاصة' }\n];\n\nconst CategoryFilter: React.FC<CategoryFilterProps> = ({ selectedCategory, onCategoryChange }) => {\n  return (\n    <div className=\"flex flex-wrap gap-3 justify-center\">\n      {categories.map((category) => (\n        <button\n          key={category.id}\n          onClick={() => onCategoryChange(category.id)}\n          className={`category-filter ${\n            selectedCategory === category.id ? '' : 'inactive'\n          }`}\n          title={category.description}\n        >\n          {category.name}\n        </button>\n      ))}\n    </div>\n  );\n};\n\nexport default CategoryFilter;\n"], "names": [], "mappings": ";;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,MAAM;QAAe,aAAa;IAAoB;IACnE;QAAE,IAAI;QAAkB,MAAM;QAA4B,aAAa;IAAiC;IACxG;QAAE,IAAI;QAAoB,MAAM;QAAwB,aAAa;IAA6B;IAClG;QAAE,IAAI;QAAqB,MAAM;QAAqB,aAAa;IAAgC;CACpG;AAED,MAAM,iBAAgD,CAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;IAC3F,qBACE,kYAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,yBACf,kYAAC;gBAEC,SAAS,IAAM,iBAAiB,SAAS,EAAE;gBAC3C,WAAW,CAAC,gBAAgB,EAC1B,qBAAqB,SAAS,EAAE,GAAG,KAAK,YACxC;gBACF,OAAO,SAAS,WAAW;0BAE1B,SAAS,IAAI;eAPT,SAAS,EAAE;;;;;;;;;;AAY1B;uCAEe", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/PostCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ninterface Post {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  tags: string[];\n  author: string;\n  date: string;\n  likes: number;\n  comments: number;\n  image?: string;\n}\n\ninterface PostCardProps {\n  post: Post;\n}\n\nconst PostCard: React.FC<PostCardProps> = ({ post }) => {\n  const [isLiked, setIsLiked] = useState(false);\n  const [likesCount, setLikesCount] = useState(post.likes);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const getCategoryName = (category: string) => {\n    switch (category) {\n      case 'social-welfare':\n        return 'برامج الرعاية الاجتماعية';\n      case 'economic-support':\n        return 'المبادرات الاقتصادية';\n      case 'private-transport':\n        return 'خدمات النقل الخاص';\n      default:\n        return 'عام';\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'social-welfare':\n        return 'bg-blue-100 text-blue-800';\n      case 'economic-support':\n        return 'bg-green-100 text-green-800';\n      case 'private-transport':\n        return 'bg-purple-100 text-purple-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const handleLike = () => {\n    setIsLiked(!isLiked);\n    setLikesCount(prev => isLiked ? prev - 1 : prev + 1);\n  };\n\n  const truncateText = (text: string, maxLength: number) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <div className=\"instagram-post\">\n      {/* Post Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">\n                {post.author.charAt(0)}\n              </span>\n            </div>\n            <div>\n              <p className=\"font-semibold text-sm\">{post.author}</p>\n              <p className=\"text-gray-500 text-xs\">{post.date}</p>\n            </div>\n          </div>\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(post.category)}`}>\n            {getCategoryName(post.category)}\n          </span>\n        </div>\n      </div>\n\n      {/* Post Content */}\n      <div className=\"p-4\">\n        <h3 className=\"font-bold text-lg mb-2 text-gray-900 arabic-text\">{post.title}</h3>\n        <div className=\"text-gray-700 mb-3 leading-relaxed arabic-text\">\n          {isExpanded ? post.description : truncateText(post.description, 150)}\n          {post.description.length > 150 && (\n            <button\n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"text-blue-500 hover:text-blue-700 mr-2 font-medium\"\n            >\n              {isExpanded ? 'عرض أقل' : 'عرض المزيد'}\n            </button>\n          )}\n        </div>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-3\">\n          {post.tags.map((tag, index) => (\n            <span\n              key={index}\n              className=\"bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs hover:bg-gray-200 transition-colors cursor-pointer\"\n            >\n              #{tag}\n            </span>\n          ))}\n        </div>\n      </div>\n\n      {/* Post Actions */}\n      <div className=\"px-4 py-3 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <button\n              onClick={handleLike}\n              className={`flex items-center space-x-1 space-x-reverse transition-all duration-200 ${\n                isLiked ? 'text-red-500' : 'text-gray-600 hover:text-red-500'\n              }`}\n            >\n              <svg\n                className={`w-6 h-6 transition-transform duration-200 ${isLiked ? 'scale-110' : ''}`}\n                fill={isLiked ? 'currentColor' : 'none'}\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n              <span className=\"text-sm font-medium\">{likesCount}</span>\n            </button>\n            <button className=\"flex items-center space-x-1 space-x-reverse text-gray-600 hover:text-blue-500 transition-colors\">\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n              </svg>\n              <span className=\"text-sm font-medium\">{post.comments}</span>\n            </button>\n            <button className=\"text-gray-600 hover:text-green-500 transition-colors\">\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\" />\n              </svg>\n            </button>\n          </div>\n          <button className=\"text-gray-600 hover:text-yellow-500 transition-colors\">\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PostCard;\n"], "names": [], "mappings": ";;;;;AAAA;;;AAmBA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qWAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qWAAQ,EAAC,KAAK,KAAK;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qWAAQ,EAAC;IAE7C,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB,WAAW,CAAC;QACZ,cAAc,CAAA,OAAQ,UAAU,OAAO,IAAI,OAAO;IACpD;IAEA,MAAM,eAAe,CAAC,MAAc;QAClC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;IACxC;IAEA,qBACE,kYAAC;QAAI,WAAU;;0BAEb,kYAAC;gBAAI,WAAU;0BACb,cAAA,kYAAC;oBAAI,WAAU;;sCACb,kYAAC;4BAAI,WAAU;;8CACb,kYAAC;oCAAI,WAAU;8CACb,cAAA,kYAAC;wCAAK,WAAU;kDACb,KAAK,MAAM,CAAC,MAAM,CAAC;;;;;;;;;;;8CAGxB,kYAAC;;sDACC,kYAAC;4CAAE,WAAU;sDAAyB,KAAK,MAAM;;;;;;sDACjD,kYAAC;4CAAE,WAAU;sDAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;sCAGnD,kYAAC;4BAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,KAAK,QAAQ,GAAG;sCAC7F,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,kYAAC;gBAAI,WAAU;;kCACb,kYAAC;wBAAG,WAAU;kCAAoD,KAAK,KAAK;;;;;;kCAC5E,kYAAC;wBAAI,WAAU;;4BACZ,aAAa,KAAK,WAAW,GAAG,aAAa,KAAK,WAAW,EAAE;4BAC/D,KAAK,WAAW,CAAC,MAAM,GAAG,qBACzB,kYAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,aAAa,YAAY;;;;;;;;;;;;kCAMhC,kYAAC;wBAAI,WAAU;kCACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,kYAAC;gCAEC,WAAU;;oCACX;oCACG;;+BAHG;;;;;;;;;;;;;;;;0BAUb,kYAAC;gBAAI,WAAU;0BACb,cAAA,kYAAC;oBAAI,WAAU;;sCACb,kYAAC;4BAAI,WAAU;;8CACb,kYAAC;oCACC,SAAS;oCACT,WAAW,CAAC,wEAAwE,EAClF,UAAU,iBAAiB,oCAC3B;;sDAEF,kYAAC;4CACC,WAAW,CAAC,0CAA0C,EAAE,UAAU,cAAc,IAAI;4CACpF,MAAM,UAAU,iBAAiB;4CACjC,QAAO;4CACP,SAAQ;sDAER,cAAA,kYAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,kYAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;;8CAEzC,kYAAC;oCAAO,WAAU;;sDAChB,kYAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,kYAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,kYAAC;4CAAK,WAAU;sDAAuB,KAAK,QAAQ;;;;;;;;;;;;8CAEtD,kYAAC;oCAAO,WAAU;8CAChB,cAAA,kYAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,kYAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAI3E,kYAAC;4BAAO,WAAU;sCAChB,cAAA,kYAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,kYAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;uCAEe", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner: React.FC = () => {\n  return (\n    <div className=\"flex justify-center items-center py-12\">\n      <div className=\"loading-spinner\"></div>\n      <span className=\"mr-3 text-gray-600 arabic-text\">جاري التحميل...</span>\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;;;AAEA,MAAM,iBAA2B;IAC/B,qBACE,kYAAC;QAAI,WAAU;;0BACb,kYAAC;gBAAI,WAAU;;;;;;0BACf,kYAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAGvD;uCAEe", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/components/StatsCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StatsCardProps {\n  totalPosts: number;\n  totalCategories: number;\n  totalLikes: number;\n}\n\nconst StatsCard: React.FC<StatsCardProps> = ({ totalPosts, totalCategories, totalLikes }) => {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"text-center\">\n          <div className=\"text-3xl font-bold text-blue-600 mb-2\">{totalPosts}</div>\n          <div className=\"text-sm text-gray-600 arabic-text\">إجمالي المنشورات</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-3xl font-bold text-green-600 mb-2\">{totalCategories}</div>\n          <div className=\"text-sm text-gray-600 arabic-text\">الفئات المتاحة</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-3xl font-bold text-red-600 mb-2\">{totalLikes}</div>\n          <div className=\"text-sm text-gray-600 arabic-text\">إجمالي الإعجابات</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatsCard;\n"], "names": [], "mappings": ";;;;;;AAQA,MAAM,YAAsC,CAAC,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE;IACtF,qBACE,kYAAC;QAAI,WAAU;kBACb,cAAA,kYAAC;YAAI,WAAU;;8BACb,kYAAC;oBAAI,WAAU;;sCACb,kYAAC;4BAAI,WAAU;sCAAyC;;;;;;sCACxD,kYAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;8BAErD,kYAAC;oBAAI,WAAU;;sCACb,kYAAC;4BAAI,WAAU;sCAA0C;;;;;;sCACzD,kYAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;8BAErD,kYAAC;oBAAI,WAAU;;sCACb,kYAAC;4BAAI,WAAU;sCAAwC;;;;;;sCACvD,kYAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;AAK7D;uCAEe", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/data/socialTransfers.ts"], "sourcesContent": ["export interface SocialTransfer {\n  id: number;\n  title: string;\n  description: string;\n  category: 'social-welfare' | 'economic-support' | 'private-transport';\n  tags: string[];\n  author: string;\n  date: string;\n  likes: number;\n  comments: number;\n  image?: string;\n}\n\nexport const socialTransfersData: SocialTransfer[] = [\n  {\n    id: 1,\n    title: \"برنامج الدعم الاجتماعي للأسر المحتاجة في قرطبة\",\n    description: \"يهدف هذا البرنامج إلى تقديم الدعم المالي والاجتماعي للأسر ذات الدخل المحدود في مدينة قرطبة. يشمل البرنامج توفير المساعدات النقدية الشهرية، والدعم الغذائي، وبرامج التدريب المهني لتحسين فرص العمل. كما يتضمن خدمات الرعاية الصحية الأساسية والدعم التعليمي للأطفال.\",\n    category: \"social-welfare\",\n    tags: [\"دعم_اجتماعي\", \"أسر_محتاجة\", \"مساعدات_مالية\", \"قرطبة\"],\n    author: \"د. أحمد الأندلسي\",\n    date: \"منذ 3 أيام\",\n    likes: 245,\n    comments: 18\n  },\n  {\n    id: 2,\n    title: \"مبادرة دعم المشاريع الصغيرة والمتوسطة\",\n    description: \"تركز هذه المبادرة على تمكين رواد الأعمال الشباب في قرطبة من خلال تقديم القروض الميسرة والاستشارات التجارية. تشمل المبادرة برامج تدريبية في إدارة الأعمال، والتسويق الرقمي، والتخطيط المالي. كما توفر مساحات عمل مشتركة ومعارض لعرض المنتجات المحلية.\",\n    category: \"economic-support\",\n    tags: [\"مشاريع_صغيرة\", \"ريادة_أعمال\", \"قروض_ميسرة\", \"تدريب\"],\n    author: \"مريم القرطبية\",\n    date: \"منذ 5 أيام\",\n    likes: 189,\n    comments: 24\n  },\n  {\n    id: 3,\n    title: \"خدمة النقل المجتمعي للمناطق النائية\",\n    description: \"خدمة نقل مبتكرة تهدف إلى ربط المناطق النائية في قرطبة بالمركز الحضري. تستخدم الخدمة حافلات صغيرة صديقة للبيئة وتطبيق ذكي لحجز الرحلات. تساعد هذه الخدمة في تحسين الوصول إلى الخدمات الأساسية مثل المستشفيات والمدارس ومراكز التسوق.\",\n    category: \"private-transport\",\n    tags: [\"نقل_مجتمعي\", \"مناطق_نائية\", \"تطبيق_ذكي\", \"بيئة\"],\n    author: \"عبد الرحمن المهندس\",\n    date: \"منذ أسبوع\",\n    likes: 156,\n    comments: 12\n  },\n  {\n    id: 4,\n    title: \"برنامج الرعاية الصحية المنزلية لكبار السن\",\n    description: \"يوفر هذا البرنامج خدمات الرعاية الصحية المنزلية لكبار السن في قرطبة، بما في ذلك الفحوصات الطبية الدورية، وإدارة الأدوية، والعلاج الطبيعي. يهدف البرنامج إلى تحسين جودة الحياة لكبار السن وتقليل الحاجة للإقامة في المستشفيات.\",\n    category: \"social-welfare\",\n    tags: [\"كبار_السن\", \"رعاية_منزلية\", \"صحة\", \"جودة_الحياة\"],\n    author: \"د. فاطمة الطبيبة\",\n    date: \"منذ أسبوعين\",\n    likes: 298,\n    comments: 31\n  },\n  {\n    id: 5,\n    title: \"صندوق دعم الابتكار التكنولوجي\",\n    description: \"صندوق استثماري يهدف إلى دعم الشركات الناشئة في مجال التكنولوجيا في قرطبة. يقدم الصندوق التمويل الأولي، والإرشاد من خبراء الصناعة، وإمكانية الوصول إلى شبكة واسعة من المستثمرين. يركز على الحلول التقنية التي تخدم المجتمع المحلي.\",\n    category: \"economic-support\",\n    tags: [\"ابتكار\", \"تكنولوجيا\", \"شركات_ناشئة\", \"استثمار\"],\n    author: \"محمد التقني\",\n    date: \"منذ 10 أيام\",\n    likes: 167,\n    comments: 19\n  },\n  {\n    id: 6,\n    title: \"شبكة سيارات الأجرة الكهربائية\",\n    description: \"مشروع رائد لإطلاق أسطول من سيارات الأجرة الكهربائية في قرطبة. يهدف المشروع إلى تقليل التلوث البيئي وتوفير خدمة نقل نظيفة وموثوقة. يتضمن المشروع محطات شحن سريع موزعة في أنحاء المدينة وتطبيق محمول لطلب الخدمة.\",\n    category: \"private-transport\",\n    tags: [\"سيارات_كهربائية\", \"بيئة\", \"نقل_نظيف\", \"تطبيق\"],\n    author: \"سارة البيئية\",\n    date: \"منذ 4 أيام\",\n    likes: 203,\n    comments: 15\n  },\n  {\n    id: 7,\n    title: \"مركز التأهيل المهني للشباب\",\n    description: \"مركز متخصص في تدريب الشباب على المهارات التقنية والحرفية المطلوبة في سوق العمل. يقدم المركز دورات في البرمجة، والتصميم الجرافيكي، والحرف اليدوية التقليدية. كما يوفر برامج التوجيه المهني وربط الخريجين بفرص العمل المناسبة.\",\n    category: \"social-welfare\",\n    tags: [\"تأهيل_مهني\", \"شباب\", \"مهارات_تقنية\", \"توظيف\"],\n    author: \"أستاذ يوسف\",\n    date: \"منذ 6 أيام\",\n    likes: 178,\n    comments: 22\n  },\n  {\n    id: 8,\n    title: \"برنامج دعم المزارعين المحليين\",\n    description: \"مبادرة لدعم المزارعين في المناطق الريفية حول قرطبة من خلال توفير البذور المحسنة، والأسمدة العضوية، والتدريب على التقنيات الزراعية الحديثة. يهدف البرنامج إلى زيادة الإنتاجية وتحسين دخل المزارعين مع الحفاظ على البيئة.\",\n    category: \"economic-support\",\n    tags: [\"زراعة\", \"مزارعين\", \"تقنيات_حديثة\", \"بيئة\"],\n    author: \"م. خالد الزراعي\",\n    date: \"منذ 8 أيام\",\n    likes: 134,\n    comments: 16\n  },\n  {\n    id: 9,\n    title: \"خدمة النقل المدرسي الآمن\",\n    description: \"خدمة نقل مخصصة للطلاب تضمن وصولهم الآمن إلى المدارس. تتميز الخدمة بحافلات مجهزة بأنظمة أمان متقدمة، وسائقين مدربين، ونظام تتبع GPS لمراقبة الرحلات. تشمل الخدمة أيضاً تطبيق للأهالي لمتابعة رحلات أطفالهم.\",\n    category: \"private-transport\",\n    tags: [\"نقل_مدرسي\", \"أمان\", \"طلاب\", \"تتبع\"],\n    author: \"أبو محمد السائق\",\n    date: \"منذ 12 يوم\",\n    likes: 267,\n    comments: 28\n  },\n  {\n    id: 10,\n    title: \"برنامج الدعم النفسي والاجتماعي\",\n    description: \"برنامج شامل لتقديم الدعم النفسي والاجتماعي لأفراد المجتمع الذين يواجهون تحديات نفسية أو اجتماعية. يتضمن البرنامج جلسات استشارة فردية وجماعية، وورش عمل للتوعية، وخط ساخن للدعم النفسي على مدار الساعة.\",\n    category: \"social-welfare\",\n    tags: [\"دعم_نفسي\", \"استشارة\", \"صحة_نفسية\", \"مجتمع\"],\n    author: \"د. ليلى النفسية\",\n    date: \"منذ 9 أيام\",\n    likes: 312,\n    comments: 35\n  }\n];\n"], "names": [], "mappings": ";;;;AAaO,MAAM,sBAAwC;IACnD;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAe;YAAc;YAAiB;SAAQ;QAC7D,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAgB;YAAe;YAAc;SAAQ;QAC5D,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAc;YAAe;YAAa;SAAO;QACxD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAa;YAAgB;YAAO;SAAc;QACzD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAU;YAAa;YAAe;SAAU;QACvD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAmB;YAAQ;YAAY;SAAQ;QACtD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAc;YAAQ;YAAgB;SAAQ;QACrD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAgB;SAAO;QAClD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAa;YAAQ;YAAQ;SAAO;QAC3C,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAY;YAAW;YAAa;SAAQ;QACnD,QAAQ;QACR,MAAM;QACN,OAAO;QACP,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Header from '@/components/Header';\nimport SearchBar from '@/components/SearchBar';\nimport CategoryFilter from '@/components/CategoryFilter';\nimport PostCard from '@/components/PostCard';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport StatsCard from '@/components/StatsCard';\nimport { socialTransfersData } from '@/data/socialTransfers';\n\nexport default function Home() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate loading time\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const filteredData = socialTransfersData.filter(item => {\n    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;\n\n    return matchesSearch && matchesCategory;\n  });\n\n  const totalLikes = socialTransfersData.reduce((sum, item) => sum + item.likes, 0);\n  const uniqueCategories = new Set(socialTransfersData.map(item => item.category)).size;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"instagram-container px-4 py-6\">\n        <StatsCard\n          totalPosts={socialTransfersData.length}\n          totalCategories={uniqueCategories}\n          totalLikes={totalLikes}\n        />\n\n        <div className=\"mb-6\">\n          <SearchBar\n            searchTerm={searchTerm}\n            onSearchChange={setSearchTerm}\n          />\n        </div>\n\n        <div className=\"mb-6\">\n          <CategoryFilter\n            selectedCategory={selectedCategory}\n            onCategoryChange={setSelectedCategory}\n          />\n        </div>\n\n        <div className=\"space-y-6\">\n          {isLoading ? (\n            <LoadingSpinner />\n          ) : filteredData.length > 0 ? (\n            filteredData.map((item) => (\n              <PostCard key={item.id} post={item} />\n            ))\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"mb-4\">\n                <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </div>\n              <p className=\"text-gray-500 text-lg arabic-text\">لا توجد نتائج للبحث الحالي</p>\n              <p className=\"text-gray-400 text-sm mt-2 arabic-text\">جرب البحث بكلمات مختلفة أو غير الفئة المحددة</p>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qWAAQ,EAAC;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,qWAAQ,EAAC;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qWAAQ,EAAC;IAE3C,IAAA,sWAAS,EAAC;QACR,wBAAwB;QACxB,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,eAAe,ySAAmB,CAAC,MAAM,CAAC,CAAA;QAC9C,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE5F,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QAExE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,aAAa,ySAAmB,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC/E,MAAM,mBAAmB,IAAI,IAAI,ySAAmB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG,IAAI;IAErF,qBACE,kYAAC;QAAI,WAAU;;0BACb,kYAAC,2RAAM;;;;;0BAEP,kYAAC;gBAAK,WAAU;;kCACd,kYAAC,8RAAS;wBACR,YAAY,ySAAmB,CAAC,MAAM;wBACtC,iBAAiB;wBACjB,YAAY;;;;;;kCAGd,kYAAC;wBAAI,WAAU;kCACb,cAAA,kYAAC,8RAAS;4BACR,YAAY;4BACZ,gBAAgB;;;;;;;;;;;kCAIpB,kYAAC;wBAAI,WAAU;kCACb,cAAA,kYAAC,mSAAc;4BACb,kBAAkB;4BAClB,kBAAkB;;;;;;;;;;;kCAItB,kYAAC;wBAAI,WAAU;kCACZ,0BACC,kYAAC,mSAAc;;;;mCACb,aAAa,MAAM,GAAG,IACxB,aAAa,GAAG,CAAC,CAAC,qBAChB,kYAAC,6RAAQ;gCAAe,MAAM;+BAAf,KAAK,EAAE;;;;sDAGxB,kYAAC;4BAAI,WAAU;;8CACb,kYAAC;oCAAI,WAAU;8CACb,cAAA,kYAAC;wCAAI,WAAU;wCAAuC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9F,cAAA,kYAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,kYAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,kYAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpE", "debugId": null}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,mLACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Pictures/%D8%A7%D9%84%D8%AA%D8%AD%D9%88%D9%84%D8%A7%D8%AA%20%D9%81%D9%8A%20%D9%82%D8%B1%D8%B7%D8%A8%D8%A9/cordoba-social-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,mLACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}