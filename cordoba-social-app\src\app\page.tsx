'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import SearchBar from '@/components/SearchBar';
import CategoryFilter from '@/components/CategoryFilter';
import PostCard from '@/components/PostCard';
import LoadingSpinner from '@/components/LoadingSpinner';
import StatsCard from '@/components/StatsCard';
import { socialTransfersData } from '@/data/socialTransfers';

export default function Home() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const filteredData = socialTransfersData.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const totalLikes = socialTransfersData.reduce((sum, item) => sum + item.likes, 0);
  const uniqueCategories = new Set(socialTransfersData.map(item => item.category)).size;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="instagram-container px-4 py-6">
        <StatsCard
          totalPosts={socialTransfersData.length}
          totalCategories={uniqueCategories}
          totalLikes={totalLikes}
        />

        <div className="mb-6">
          <SearchBar
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
          />
        </div>

        <div className="mb-6">
          <CategoryFilter
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
        </div>

        <div className="space-y-6">
          {isLoading ? (
            <LoadingSpinner />
          ) : filteredData.length > 0 ? (
            filteredData.map((item) => (
              <PostCard key={item.id} post={item} />
            ))
          ) : (
            <div className="text-center py-12">
              <div className="mb-4">
                <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <p className="text-gray-500 text-lg arabic-text">لا توجد نتائج للبحث الحالي</p>
              <p className="text-gray-400 text-sm mt-2 arabic-text">جرب البحث بكلمات مختلفة أو غير الفئة المحددة</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
