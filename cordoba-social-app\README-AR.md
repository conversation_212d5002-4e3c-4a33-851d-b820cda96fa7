# تطبيق التحولات الاجتماعية في قرطبة

تطبيق ويب مشابه لإنستغرام يركز على البحث والاستكشاف في التحولات الاجتماعية في مدينة قرطبة، مطور باللغة العربية مع دعم كامل للتوجه من اليمين إلى اليسار.

## المميزات الرئيسية

### 🔍 البحث والاستكشاف
- بحث متقدم في المحتوى والعناوين والوسوم
- فلترة حسب الفئات المختلفة
- واجهة بحث سهلة الاستخدام

### 📱 واجهة مشابهة لإنستغرام
- تصميم عصري وجذاب
- بطاقات منشورات تفاعلية
- نظام الإعجابات والتعليقات
- رأس صفحة ثابت مع شعار التطبيق

### 🏷️ فئات التحولات الاجتماعية
1. **برامج الرعاية الاجتماعية** - برامج الدعم الاجتماعي والرعاية
2. **المبادرات الاقتصادية** - المبادرات والدعم الاقتصادي
3. **خدمات النقل الخاص** - خدمات النقل والمواصلات الخاصة

### 🌐 دعم اللغة العربية
- واجهة مستخدم باللغة العربية بالكامل
- دعم التوجه من اليمين إلى اليسار (RTL)
- خطوط عربية محسنة للقراءة

### 📊 إحصائيات سريعة
- عدد المنشورات الإجمالي
- عدد الفئات المتاحة
- إجمالي الإعجابات

## التقنيات المستخدمة

- **Next.js 15** - إطار عمل React للتطبيقات الحديثة
- **TypeScript** - لكتابة كود آمن ومنظم
- **Tailwind CSS** - لتصميم واجهة المستخدم
- **React Hooks** - لإدارة الحالة والتفاعل

## كيفية التشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التشغيل

1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd cordoba-social-app
   ```

2. **تثبيت التبعيات**
   ```bash
   npm install
   ```

3. **تشغيل الخادم المحلي**
   ```bash
   npm run dev
   ```

4. **فتح التطبيق**
   افتح المتصفح وانتقل إلى: `http://localhost:3000`

## هيكل المشروع

```
cordoba-social-app/
├── src/
│   ├── app/
│   │   ├── globals.css          # الأنماط العامة
│   │   ├── layout.tsx           # تخطيط التطبيق الأساسي
│   │   └── page.tsx             # الصفحة الرئيسية
│   ├── components/
│   │   ├── Header.tsx           # رأس الصفحة
│   │   ├── SearchBar.tsx        # شريط البحث
│   │   ├── CategoryFilter.tsx   # فلتر الفئات
│   │   ├── PostCard.tsx         # بطاقة المنشور
│   │   ├── LoadingSpinner.tsx   # مؤشر التحميل
│   │   └── StatsCard.tsx        # بطاقة الإحصائيات
│   └── data/
│       └── socialTransfers.ts   # بيانات التحولات الاجتماعية
├── public/                      # الملفات العامة
└── README-AR.md                 # هذا الملف
```

## البيانات الوهمية

يحتوي التطبيق على 10 منشورات وهمية تغطي مختلف جوانب التحولات الاجتماعية في قرطبة:

- برامج الدعم الاجتماعي للأسر
- مبادرات دعم المشاريع الصغيرة
- خدمات النقل المجتمعي
- برامج الرعاية الصحية
- صناديق الابتكار التكنولوجي
- وغيرها من المبادرات الاجتماعية والاقتصادية

## المساهمة

نرحب بالمساهمات لتحسين التطبيق. يمكنك:

1. إضافة المزيد من البيانات الحقيقية
2. تحسين التصميم والواجهة
3. إضافة مميزات جديدة
4. تحسين الأداء والاستجابة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة**: هذا التطبيق تم تطويره كنموذج أولي لاستكشاف التحولات الاجتماعية في قرطبة. البيانات المستخدمة هي بيانات وهمية لأغراض العرض والتطوير.
