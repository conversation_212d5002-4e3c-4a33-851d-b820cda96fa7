# دليل النشر على Netlify

## خطوات النشر

### 1. إعداد المشروع للنشر
تم إعداد المشروع بالفعل للنشر على Netlify مع الإعدادات التالية:

- **ملف `netlify.toml`**: يحتوي على إعدادات البناء والنشر
- **ملف `next.config.ts`**: معدل لدعم التصدير الثابت
- **ملف `package.json`**: يحتوي على scripts البناء المطلوبة

### 2. النشر على Netlify

#### الطريقة الأولى: النشر المباشر
1. اذهب إلى [netlify.com](https://netlify.com)
2. قم بإنشاء حساب أو تسجيل الدخول
3. اضغط على "Add new site" > "Deploy manually"
4. اسحب وأفلت مجلد `out` إلى المنطقة المخصصة
5. انتظر حتى يكتمل النشر

#### الطريقة الثانية: النشر من Git (مستحسن)
1. ارفع المشروع إلى GitHub/GitLab/Bitbucket
2. في Netlify، اضغط على "Add new site" > "Import from Git"
3. اختر مزود Git الخاص بك وحدد المستودع
4. استخدم الإعدادات التالية:
   - **Build command**: `npm run build`
   - **Publish directory**: `out`
   - **Node version**: `18`

### 3. إعدادات البناء
```toml
[build]
  command = "npm run build"
  publish = "out"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 4. التحقق من النشر
بعد النشر الناجح:
- ستحصل على رابط مثل: `https://amazing-app-name.netlify.app`
- تأكد من أن التطبيق يعمل بشكل صحيح
- تحقق من دعم اللغة العربية والتوجه RTL

### 5. تخصيص النطاق (اختياري)
يمكنك تخصيص اسم النطاق من:
- Site settings > Domain management
- اختر اسم مخصص مثل: `cordoba-social-transforms.netlify.app`

### 6. إعدادات إضافية مفيدة

#### تفعيل HTTPS
- يتم تفعيله تلقائياً بواسطة Netlify

#### إعداد متغيرات البيئة (إذا لزم الأمر)
- Site settings > Environment variables
- أضف أي متغيرات مطلوبة

#### تفعيل النشر التلقائي
- عند ربط المشروع بـ Git، سيتم النشر تلقائياً عند كل push

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في البناء**
   ```
   Build failed: Command failed with exit code 1
   ```
   **الحل**: تأكد من أن `npm run build` يعمل محلياً بدون أخطاء

2. **مشكلة في الخطوط العربية**
   **الحل**: تأكد من أن Google Fonts يتم تحميلها بشكل صحيح

3. **مشكلة في التوجه RTL**
   **الحل**: تحقق من إعدادات CSS و `direction: rtl`

4. **صفحة 404 عند التنقل**
   **الحل**: تأكد من وجود إعدادات redirects في `netlify.toml`

## معلومات إضافية

- **حجم التطبيق**: حوالي 107 KB (First Load JS)
- **عدد الصفحات**: 2 (الرئيسية و 404)
- **نوع التطبيق**: Static Site Generation (SSG)
- **وقت البناء**: حوالي 12 ثانية

## الدعم

إذا واجهت أي مشاكل في النشر:
1. تحقق من logs البناء في Netlify
2. تأكد من أن جميع التبعيات مثبتة
3. اختبر البناء محلياً أولاً: `npm run build`

---

**ملاحظة**: هذا التطبيق جاهز للنشر ويعمل بشكل كامل مع دعم اللغة العربية والتصميم المتجاوب.
