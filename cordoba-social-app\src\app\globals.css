@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100..900&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Noto Sans Arabic', Arial, sans-serif;
  direction: rtl;
}

/* Instagram-like styles */
.instagram-container {
  max-width: 935px;
  margin: 0 auto;
}

.instagram-header {
  border-bottom: 1px solid #dbdbdb;
  background: white;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.instagram-post {
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  background: white;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.instagram-post:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  background: #efefef;
  border: 1px solid #dbdbdb;
  border-radius: 24px;
  padding: 12px 20px;
  font-size: 14px;
  width: 100%;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #0095f6;
  background: white;
  box-shadow: 0 0 0 2px rgba(0, 149, 246, 0.1);
}

.category-filter {
  background: #0095f6;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-filter:hover {
  background: #1877f2;
  transform: translateY(-1px);
}

.category-filter.inactive {
  background: #efefef;
  color: #262626;
  border: 1px solid #dbdbdb;
}

.category-filter.inactive:hover {
  background: #dbdbdb;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .instagram-container {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .category-filter {
    font-size: 12px;
    padding: 8px 16px;
  }
  
  .search-input {
    font-size: 16px;
  }
}

/* Arabic text improvements */
.arabic-text {
  font-family: 'Noto Sans Arabic', Arial, sans-serif;
  line-height: 1.6;
}

/* Loading animation */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0095f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
