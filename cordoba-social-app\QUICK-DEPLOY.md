# 🚀 النشر السريع على Netlify

## الطريقة الأسرع (5 دقائق)

### 1. تحضير الملفات
```bash
# تأكد من أن البناء يعمل
npm run build
```

### 2. النشر المباشر
1. اذهب إلى [netlify.com](https://netlify.com)
2. سجل دخول أو أنشئ حساب جديد
3. اضغط **"Add new site"** → **"Deploy manually"**
4. اسحب مجلد `out` من المشروع إلى المنطقة المخصصة
5. انتظر النشر (حوالي 30 ثانية)

### 3. النتيجة
- ستحصل على رابط مثل: `https://amazing-name-123456.netlify.app`
- التطبيق جاهز للاستخدام فوراً!

## تخصيص الاسم (اختياري)
- اذهب إلى **Site settings** → **Domain management**
- غير الاسم إلى شيء مثل: `cordoba-social-transforms`

## ✅ التحقق من النجاح
- [ ] التطبيق يفتح بدون أخطاء
- [ ] النص العربي يظهر بشكل صحيح
- [ ] البحث يعمل
- [ ] الفلاتر تعمل
- [ ] الإعجابات تعمل

## 🔧 إذا واجهت مشاكل
1. تأكد من أن `npm run build` يعمل محلياً
2. تحقق من أن مجلد `out` موجود وليس فارغ
3. راجع logs البناء في Netlify

---
**الوقت المتوقع للنشر: 2-5 دقائق** ⏱️
