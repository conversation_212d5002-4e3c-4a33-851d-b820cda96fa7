{"version": 4, "routes": {"/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/_not-found": {"initialStatus": 404, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/_not-found", "dataRoute": "/_not-found.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "1f024dee9645c75aae496a93eeab38f9", "previewModeSigningKey": "9bc72875fdd0369af6d42b05c618fe9789c791fabed3cdde7ad6407b410ff483", "previewModeEncryptionKey": "6e09d0ab2da3336b1fcdfd7b5da276d1bcda7785c5b68133498893b9121af06c"}}