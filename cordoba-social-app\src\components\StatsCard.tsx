import React from 'react';

interface StatsCardProps {
  totalPosts: number;
  totalCategories: number;
  totalLikes: number;
}

const StatsCard: React.FC<StatsCardProps> = ({ totalPosts, totalCategories, totalLikes }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-2">{totalPosts}</div>
          <div className="text-sm text-gray-600 arabic-text">إجمالي المنشورات</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-green-600 mb-2">{totalCategories}</div>
          <div className="text-sm text-gray-600 arabic-text">الفئات المتاحة</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-red-600 mb-2">{totalLikes}</div>
          <div className="text-sm text-gray-600 arabic-text">إجمالي الإعجابات</div>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
