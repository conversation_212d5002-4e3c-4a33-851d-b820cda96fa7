# Cordoba Social Transformations App

An Instagram-like web application focused on exploring social transformations in Cordoba, developed in Arabic with full RTL support.

## 🌟 Features

### 🔍 Advanced Search & Discovery
- Advanced search through content, titles, and tags
- Category-based filtering
- User-friendly search interface

### 📱 Instagram-like Interface
- Modern and attractive design
- Interactive post cards
- Like and comment system
- Sticky header with app logo

### 🏷️ Social Transformation Categories
1. **Social Welfare Programs** - Social support and care programs
2. **Economic Initiatives** - Economic initiatives and support
3. **Private Transportation Services** - Transportation and mobility services

### 🌐 Arabic Language Support
- Complete Arabic user interface
- Right-to-left (RTL) direction support
- Optimized Arabic fonts for readability

### 📊 Quick Statistics
- Total posts count
- Available categories count
- Total likes count

## 🛠️ Technologies Used

- **Next.js 15** - Modern React framework
- **TypeScript** - Type-safe code
- **Tailwind CSS** - UI design
- **React Hooks** - State management and interaction

## 🚀 Getting Started

### Prerequisites
- Node.js (version 18 or higher)
- npm or yarn

### Installation & Running

1. **Clone the project**
   ```bash
   git clone [repository-url]
   cd cordoba-social-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run development server**
   ```bash
   npm run dev
   ```

4. **Open the application**
   Navigate to: `http://localhost:3000`

## 📦 Deployment

### Deploy to Netlify

The app is ready for deployment on Netlify:

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Deploy to Netlify**
   - Upload the `out` folder to Netlify
   - Or connect your Git repository for automatic deployments

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions.

## 📁 Project Structure

```
cordoba-social-app/
├── src/
│   ├── app/
│   │   ├── globals.css          # Global styles
│   │   ├── layout.tsx           # App layout
│   │   └── page.tsx             # Main page
│   ├── components/
│   │   ├── Header.tsx           # Header component
│   │   ├── SearchBar.tsx        # Search bar
│   │   ├── CategoryFilter.tsx   # Category filter
│   │   ├── PostCard.tsx         # Post card
│   │   ├── LoadingSpinner.tsx   # Loading indicator
│   │   └── StatsCard.tsx        # Statistics card
│   └── data/
│       └── socialTransfers.ts   # Social transformations data
├── public/                      # Public files
├── out/                         # Build output (for deployment)
├── netlify.toml                 # Netlify configuration
├── DEPLOYMENT.md                # Deployment guide
└── README-AR.md                 # Arabic README
```

## 📊 Sample Data

The app includes 10 sample posts covering various aspects of social transformations in Cordoba:

- Family social support programs
- Small business support initiatives
- Community transportation services
- Healthcare programs
- Technology innovation funds
- And other social and economic initiatives

## 🤝 Contributing

Contributions are welcome! You can:

1. Add more real data
2. Improve design and interface
3. Add new features
4. Improve performance and responsiveness

## 📄 License

This project is open source and available for use and development.

---

**Note**: This application was developed as a prototype to explore social transformations in Cordoba. The data used is sample data for demonstration and development purposes.
